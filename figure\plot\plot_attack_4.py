import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import math

# --- Configuration ---
FIGURE_BASE_FILENAME = "ue_sinr_by_sender_sim4"
UES_PER_CHART = 4  # Maximum number of UEs per chart
SIMULATION = "simulation_4"
TARGET_UES = ["NormalUE-0", "NormalUE-1", "NormalUE-2", "NormalUE-3", "NormalUE-4",
              "NormalUE-5", "NormalUE-6", "NormalUE-7", "NormalUE-8", "NormalUE-9"]  # All UEs to analyze

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
output_dir = script_dir

def load_ue_sinr_data(ue_name):
    """
    Load SINR data for a specific UE, grouped by sender UE
    """
    data_path = os.path.join(script_dir, "..", "..", "logs", "ue_logs", SIMULATION, f"{ue_name}.csv")

    if not os.path.exists(data_path):
        print(f"Data file not found: {data_path}")
        return {}

    try:
        # Try different encodings
        df = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                df = pd.read_csv(data_path, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue

        if df is None:
            print(f"Could not read {data_path} with any encoding")
            return {}

        # Filter rows that have SINR data and Related_UE_ID (received messages)
        sinr_data = df[
            (df['SINR_dB'] != 'N/A') &
            (df['Related_UE_ID'] != 'N/A') &
            (df['Related_UE_ID'].notna())
        ].copy()

        if sinr_data.empty:
            return {}

        # Convert SINR_dB to numeric
        sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
        sinr_data = sinr_data.dropna(subset=['SINR_dB'])

        # Group by sender UE
        grouped_data = {}
        for sender_ue in sinr_data['Related_UE_ID'].unique():
            sender_data = sinr_data[sinr_data['Related_UE_ID'] == sender_ue]
            if not sender_data.empty:
                grouped_data[sender_ue] = sender_data[['Time_ms', 'SINR_dB']].copy()

        return grouped_data

    except Exception as e:
        print(f"Error processing {data_path}: {e}")
        return {}

# --- Load Data for All UEs ---
print(f"Loading SINR data for all UEs from {SIMULATION}...")
all_ue_data = {}

for ue_name in TARGET_UES:
    ue_data = load_ue_sinr_data(ue_name)
    if ue_data:
        all_ue_data[ue_name] = ue_data
        print(f"  {ue_name}: {len(ue_data)} senders")

if not all_ue_data:
    print("No SINR data found for any UE. Please check the data files.")
    exit()

print(f"Successfully loaded data for {len(all_ue_data)} UEs")

# --- Create Multiple Plots with Subplots ---
plt.style.use('seaborn-v0_8-whitegrid')

# Convert all_ue_data to list for easier grouping
ue_list = list(all_ue_data.items())
total_ues = len(ue_list)
num_images = math.ceil(total_ues / UES_PER_CHART)

print(f"Creating {num_images} images for {total_ues} UEs ({UES_PER_CHART} subplots per image)")

# Define colors and line styles for different senders
colors = plt.cm.tab10(np.linspace(0, 1, 10))
line_styles = ['-', '--', '-.', ':', '-', '--', '-.', ':', '-', '--']

# Create images with subplots
for img_idx in range(num_images):
    start_idx = img_idx * UES_PER_CHART
    end_idx = min(start_idx + UES_PER_CHART, total_ues)
    image_ues = ue_list[start_idx:end_idx]
    num_subplots = len(image_ues)

    # Create subplot layout (2x2 for up to 4 UEs)
    if num_subplots == 1:
        fig, axes = plt.subplots(1, 1, figsize=(16, 8))
        axes = [axes]
    elif num_subplots == 2:
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    elif num_subplots == 3:
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        axes[3].set_visible(False)  # Hide the 4th subplot
    else:  # num_subplots == 4
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()

    # Plot each UE in its own subplot
    for i, (ue_name, sender_data) in enumerate(image_ues):
        ax = axes[i]

        # Plot SINR for each sender to this UE
        for j, (sender_ue, data) in enumerate(sender_data.items()):
            color = colors[j % len(colors)]
            line_style = line_styles[j % len(line_styles)]

            # Sort by time for proper line plotting
            data_sorted = data.sort_values('Time_ms')

            # Plot SINR line
            ax.plot(data_sorted['Time_ms'], data_sorted['SINR_dB'],
                   color=color, linestyle=line_style, linewidth=1.5, alpha=0.8,
                   label=f'{sender_ue}', marker='o', markersize=1.5)

        # Subplot formatting
        ax.set_title(f'{ue_name} Received SINR by Sender', fontsize=12, fontweight='bold')
        ax.set_xlabel('Time (ms)', fontsize=10)
        ax.set_ylabel('SINR (dB)', fontsize=10)
        ax.set_xlim(0, 5000)  # Fixed X-axis range from 0 to 5000 ms
        ax.legend(fontsize=8, loc='best')
        ax.grid(True, alpha=0.3)

    # Overall title for the image
    fig.suptitle(f'UE SINR by Sender Vehicle ({SIMULATION.replace("_", " ").title()}) - Image {img_idx + 1}/{num_images}',
                 fontsize=16, fontweight='bold')

    plt.tight_layout()

    # Save each image
    output_filename = f"{FIGURE_BASE_FILENAME}_part{img_idx + 1}.png"
    output_path = os.path.join(output_dir, output_filename)

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Image {img_idx + 1} with {num_subplots} subplots saved to {output_path}")

    plt.show()

# --- Print Detailed Statistics ---
print(f"\n=== DETAILED STATISTICS FOR ALL UEs ===")
for ue_name, sender_data in all_ue_data.items():
    print(f"\n{ue_name}:")
    for sender_ue, data in sender_data.items():
        print(f"  {sender_ue}:")
        print(f"    Measurements: {len(data)}")
        print(f"    Mean SINR: {data['SINR_dB'].mean():.2f} dB")
        print(f"    Std SINR: {data['SINR_dB'].std():.2f} dB")
        print(f"    Min SINR: {data['SINR_dB'].min():.2f} dB")
        print(f"    Max SINR: {data['SINR_dB'].max():.2f} dB")

# All charts have been saved and displayed above
