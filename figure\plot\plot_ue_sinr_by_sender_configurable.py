import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# --- Configuration ---
TARGET_UE = "NormalUE-1"  # Change this to plot different UE
SIMULATION = "simulation_3"  # Change this to simulation_3 or simulation_4
FIGURE_FILENAME = f"{TARGET_UE.lower()}_sinr_by_sender_{SIMULATION}.png"

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, "..", "..", "logs", "ue_logs", SIMULATION, f"{TARGET_UE}.csv")
output_path = os.path.join(script_dir, FIGURE_FILENAME)

def load_ue_sinr_data():
    """
    Load SINR data for the target UE, grouped by sender UE
    """
    if not os.path.exists(data_path):
        print(f"Data file not found: {data_path}")
        return {}
    
    try:
        # Try different encodings
        df = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                df = pd.read_csv(data_path, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print(f"Could not read {data_path} with any encoding")
            return {}
        
        # Filter rows that have SINR data and Related_UE_ID (received messages)
        sinr_data = df[
            (df['SINR_dB'] != 'N/A') & 
            (df['Related_UE_ID'] != 'N/A') & 
            (df['Related_UE_ID'].notna())
        ].copy()
        
        if sinr_data.empty:
            print("No SINR data found with Related_UE_ID")
            return {}
        
        # Convert SINR_dB to numeric
        sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
        sinr_data = sinr_data.dropna(subset=['SINR_dB'])
        
        # Group by sender UE
        grouped_data = {}
        for sender_ue in sinr_data['Related_UE_ID'].unique():
            sender_data = sinr_data[sinr_data['Related_UE_ID'] == sender_ue]
            if not sender_data.empty:
                grouped_data[sender_ue] = sender_data[['Time_ms', 'SINR_dB', 'EventType']].copy()
        
        return grouped_data
        
    except Exception as e:
        print(f"Error processing {data_path}: {e}")
        return {}

# --- Load Data ---
print(f"Loading {TARGET_UE} SINR data from {SIMULATION} grouped by sender...")
sender_data = load_ue_sinr_data()

if not sender_data:
    print("No SINR data found. Please check the data file and configuration.")
    exit()

print(f"Found SINR data from {len(sender_data)} different senders:")
for sender in sender_data.keys():
    print(f"  - {sender}: {len(sender_data[sender])} measurements")

# --- Create Plot ---
plt.style.use('seaborn-v0_8-whitegrid')
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 12))

# Define colors for different senders
colors = plt.cm.tab10(np.linspace(0, 1, len(sender_data)))
line_styles = ['-', '--', '-.', ':', '-', '--', '-.', ':', '-', '--']

# Plot 1: Time series of SINR by sender
for i, (sender_ue, data) in enumerate(sender_data.items()):
    color = colors[i % len(colors)]
    line_style = line_styles[i % len(line_styles)]
    
    # Sort by time for proper line plotting
    data_sorted = data.sort_values('Time_ms')
    
    ax1.plot(data_sorted['Time_ms'], data_sorted['SINR_dB'], 
           color=color, linestyle=line_style, linewidth=1.5, alpha=0.8,
           label=f'{sender_ue} (μ={data["SINR_dB"].mean():.1f}dB)', 
           marker='o', markersize=1.5)

ax1.set_title(f'{TARGET_UE} Received SINR by Sender Vehicle ({SIMULATION.replace("_", " ").title()})', 
              fontsize=16, fontweight='bold')
ax1.set_xlabel('Time (ms)', fontsize=12)
ax1.set_ylabel('SINR (dB)', fontsize=12)
ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
ax1.grid(True, alpha=0.3)

# Plot 2: Box plot of SINR distribution by sender
sinr_values = []
sender_labels = []
for sender_ue, data in sender_data.items():
    sinr_values.append(data['SINR_dB'].values)
    sender_labels.append(sender_ue)

box_plot = ax2.boxplot(sinr_values, tick_labels=sender_labels, patch_artist=True)

# Color the box plots
for patch, color in zip(box_plot['boxes'], colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.7)

ax2.set_title(f'{TARGET_UE} SINR Distribution by Sender Vehicle', fontsize=14, fontweight='bold')
ax2.set_xlabel('Sender Vehicle', fontsize=12)
ax2.set_ylabel('SINR (dB)', fontsize=12)
ax2.tick_params(axis='x', rotation=45)
ax2.grid(True, alpha=0.3)

plt.tight_layout()

# --- Print Detailed Statistics ---
print(f"\n=== DETAILED STATISTICS FOR {TARGET_UE} in {SIMULATION} ===")
overall_stats = []
for sender_ue, data in sender_data.items():
    mean_sinr = data['SINR_dB'].mean()
    median_sinr = data['SINR_dB'].median()
    std_sinr = data['SINR_dB'].std()
    min_sinr = data['SINR_dB'].min()
    max_sinr = data['SINR_dB'].max()
    
    print(f"\n{sender_ue}:")
    print(f"  Measurements: {len(data)}")
    print(f"  Mean SINR: {mean_sinr:.2f} dB")
    print(f"  Median SINR: {median_sinr:.2f} dB")
    print(f"  Std SINR: {std_sinr:.2f} dB")
    print(f"  Min SINR: {min_sinr:.2f} dB")
    print(f"  Max SINR: {max_sinr:.2f} dB")
    print(f"  Time range: {data['Time_ms'].min():.0f} - {data['Time_ms'].max():.0f} ms")
    
    overall_stats.append({
        'Sender': sender_ue,
        'Mean': mean_sinr,
        'Std': std_sinr,
        'Count': len(data)
    })

# Print summary ranking
print(f"\n=== SINR RANKING (Best to Worst) ===")
overall_stats.sort(key=lambda x: x['Mean'], reverse=True)
for i, stats in enumerate(overall_stats, 1):
    print(f"{i}. {stats['Sender']}: {stats['Mean']:.2f} ± {stats['Std']:.2f} dB ({stats['Count']} measurements)")

# Save plot
output_dir = os.path.dirname(output_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"\n{TARGET_UE} SINR by sender plot saved to {output_path}")
plt.show()
