import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# --- Configuration ---
FIGURE_FILENAME = "individual_ue_sinr_sim3_sim4.png"
UE_TO_PLOT = ['NormalUE-0', 'NormalUE-1', 'NormalUE-2', 'NormalUE-3']  # Specify which UEs to plot

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
sim3_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_3")
sim4_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_4")
output_path = os.path.join(script_dir, FIGURE_FILENAME)

def load_ue_sinr_data(simulation_path, ue_name, simulation_label):
    """
    Load SINR data for a specific UE
    """
    csv_file = os.path.join(simulation_path, f"{ue_name}.csv")
    
    if not os.path.exists(csv_file):
        print(f"File not found: {csv_file}")
        return pd.DataFrame()
    
    try:
        # Try different encodings
        df = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                df = pd.read_csv(csv_file, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print(f"Could not read {csv_file} with any encoding")
            return pd.DataFrame()
        
        # Filter rows that have SINR data (not N/A)
        sinr_data = df[df['SINR_dB'] != 'N/A'].copy()
        if not sinr_data.empty:
            # Convert SINR_dB to numeric
            sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
            sinr_data = sinr_data.dropna(subset=['SINR_dB'])
            
            if not sinr_data.empty:
                sinr_data['UE_ID'] = ue_name
                sinr_data['Simulation'] = simulation_label
                return sinr_data[['Time_ms', 'SINR_dB', 'UE_ID', 'Simulation']]
        
        return pd.DataFrame()
        
    except Exception as e:
        print(f"Error processing {csv_file}: {e}")
        return pd.DataFrame()

# --- Create Plot ---
plt.style.use('seaborn-v0_8-whitegrid')
fig, axes = plt.subplots(2, 2, figsize=(20, 12))
axes = axes.flatten()

colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']

for i, ue_name in enumerate(UE_TO_PLOT):
    if i >= len(axes):
        break
        
    ax = axes[i]
    
    # Load data for both simulations
    sim3_data = load_ue_sinr_data(sim3_path, ue_name, "Simulation 3")
    sim4_data = load_ue_sinr_data(sim4_path, ue_name, "Simulation 4")
    
    # Plot Simulation 3
    if not sim3_data.empty:
        ax.plot(sim3_data['Time_ms'], sim3_data['SINR_dB'], 
               color='blue', alpha=0.7, linewidth=1, label='Simulation 3')
    
    # Plot Simulation 4
    if not sim4_data.empty:
        ax.plot(sim4_data['Time_ms'], sim4_data['SINR_dB'], 
               color='red', alpha=0.7, linewidth=1, label='Simulation 4')
    
    ax.set_title(f'{ue_name} SINR over Time', fontsize=12)
    ax.set_xlabel('Time (ms)', fontsize=10)
    ax.set_ylabel('SINR (dB)', fontsize=10)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Print statistics
    if not sim3_data.empty:
        print(f"\n{ue_name} - Simulation 3:")
        print(f"  Mean SINR: {sim3_data['SINR_dB'].mean():.2f} dB")
        print(f"  Std SINR:  {sim3_data['SINR_dB'].std():.2f} dB")
        print(f"  Min SINR:  {sim3_data['SINR_dB'].min():.2f} dB")
        print(f"  Max SINR:  {sim3_data['SINR_dB'].max():.2f} dB")
    
    if not sim4_data.empty:
        print(f"\n{ue_name} - Simulation 4:")
        print(f"  Mean SINR: {sim4_data['SINR_dB'].mean():.2f} dB")
        print(f"  Std SINR:  {sim4_data['SINR_dB'].std():.2f} dB")
        print(f"  Min SINR:  {sim4_data['SINR_dB'].min():.2f} dB")
        print(f"  Max SINR:  {sim4_data['SINR_dB'].max():.2f} dB")

plt.suptitle('Individual UE SINR Comparison (Simulation 3 vs Simulation 4)', fontsize=16)
plt.tight_layout()

# Ensure the output directory exists
output_dir = os.path.dirname(output_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"\nIndividual UE SINR plot saved to {output_path}")
plt.show()
