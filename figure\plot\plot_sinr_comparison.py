import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from glob import glob

# --- Configuration ---
FIGURE_FILENAME = "sinr_comparison_sim3_sim4.png"

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
sim3_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_3")
sim4_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_4")
output_path = os.path.join(script_dir, FIGURE_FILENAME)

def load_and_process_sinr_data(simulation_path, simulation_name):
    """
    Load all CSV files from a simulation directory and extract SINR data
    """
    csv_files = glob(os.path.join(simulation_path, "*.csv"))
    all_sinr_data = []
    
    for csv_file in csv_files:
        try:
            # Try different encodings to handle potential encoding issues
            df = None
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if df is None:
                print(f"Could not read {csv_file} with any encoding")
                continue

            # Filter rows that have SINR data (not N/A)
            sinr_data = df[df['SINR_dB'] != 'N/A'].copy()
            if not sinr_data.empty:
                # Convert SINR_dB to numeric
                sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
                sinr_data = sinr_data.dropna(subset=['SINR_dB'])

                if not sinr_data.empty:
                    # Add UE identifier
                    ue_name = os.path.basename(csv_file).replace('.csv', '')
                    sinr_data['UE_ID'] = ue_name
                    sinr_data['Simulation'] = simulation_name
                    all_sinr_data.append(sinr_data[['Time_ms', 'SINR_dB', 'UE_ID', 'Simulation']])
        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue
    
    if all_sinr_data:
        return pd.concat(all_sinr_data, ignore_index=True)
    else:
        return pd.DataFrame()

def calculate_average_sinr_over_time(df, time_window=100):
    """
    Calculate average SINR over time windows for each simulation
    """
    if df.empty:
        return pd.DataFrame()
    
    max_time = df['Time_ms'].max()
    time_bins = np.arange(0, max_time + time_window, time_window)
    
    avg_sinr_data = []
    
    for i in range(len(time_bins) - 1):
        start_time, end_time = time_bins[i], time_bins[i+1]
        
        window_data = df[
            (df['Time_ms'] >= start_time) & (df['Time_ms'] < end_time)
        ]
        
        if not window_data.empty:
            avg_sinr = window_data['SINR_dB'].mean()
            std_sinr = window_data['SINR_dB'].std()
            count = len(window_data)
            
            avg_sinr_data.append({
                'Time': (start_time + end_time) / 2,
                'Avg_SINR': avg_sinr,
                'Std_SINR': std_sinr,
                'Count': count
            })
    
    return pd.DataFrame(avg_sinr_data)

# --- Load Data ---
print("Loading simulation_3 data...")
sim3_data = load_and_process_sinr_data(sim3_path, "Simulation 3")

print("Loading simulation_4 data...")
sim4_data = load_and_process_sinr_data(sim4_path, "Simulation 4")

if sim3_data.empty and sim4_data.empty:
    print("No SINR data found in either simulation. Please check the data files.")
    exit()

# --- Create Plot ---
plt.style.use('seaborn-v0_8-whitegrid')
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

# Plot 1: Individual UE SINR over time
colors_sim3 = plt.cm.Blues(np.linspace(0.3, 0.9, len(sim3_data['UE_ID'].unique()) if not sim3_data.empty else 1))
colors_sim4 = plt.cm.Reds(np.linspace(0.3, 0.9, len(sim4_data['UE_ID'].unique()) if not sim4_data.empty else 1))

# Plot Simulation 3 data
if not sim3_data.empty:
    for i, ue_id in enumerate(sim3_data['UE_ID'].unique()):
        ue_data = sim3_data[sim3_data['UE_ID'] == ue_id]
        ax1.scatter(ue_data['Time_ms'], ue_data['SINR_dB'], 
                   color=colors_sim3[i], alpha=0.6, s=10, 
                   label=f'Sim3-{ue_id}')

# Plot Simulation 4 data
if not sim4_data.empty:
    for i, ue_id in enumerate(sim4_data['UE_ID'].unique()):
        ue_data = sim4_data[sim4_data['UE_ID'] == ue_id]
        ax1.scatter(ue_data['Time_ms'], ue_data['SINR_dB'], 
                   color=colors_sim4[i], alpha=0.6, s=10, 
                   label=f'Sim4-{ue_id}')

ax1.set_title('SINR over Time - Individual UEs (Simulation 3 vs Simulation 4)', fontsize=14)
ax1.set_xlabel('Time (ms)', fontsize=12)
ax1.set_ylabel('SINR (dB)', fontsize=12)
ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
ax1.grid(True, alpha=0.3)

# Plot 2: Average SINR comparison
if not sim3_data.empty:
    sim3_avg = calculate_average_sinr_over_time(sim3_data)
    if not sim3_avg.empty:
        ax2.plot(sim3_avg['Time'], sim3_avg['Avg_SINR'], 
                marker='o', linestyle='-', linewidth=2, markersize=4,
                color='blue', label='Simulation 3 (Average)', zorder=10)
        ax2.fill_between(sim3_avg['Time'], 
                        sim3_avg['Avg_SINR'] - sim3_avg['Std_SINR'], 
                        sim3_avg['Avg_SINR'] + sim3_avg['Std_SINR'], 
                        alpha=0.2, color='blue')

if not sim4_data.empty:
    sim4_avg = calculate_average_sinr_over_time(sim4_data)
    if not sim4_avg.empty:
        ax2.plot(sim4_avg['Time'], sim4_avg['Avg_SINR'], 
                marker='s', linestyle='-', linewidth=2, markersize=4,
                color='red', label='Simulation 4 (Average)', zorder=10)
        ax2.fill_between(sim4_avg['Time'], 
                        sim4_avg['Avg_SINR'] - sim4_avg['Std_SINR'], 
                        sim4_avg['Avg_SINR'] + sim4_avg['Std_SINR'], 
                        alpha=0.2, color='red')

ax2.set_title('Average SINR Comparison (Simulation 3 vs Simulation 4)', fontsize=14)
ax2.set_xlabel('Time (ms)', fontsize=12)
ax2.set_ylabel('Average SINR (dB)', fontsize=12)
ax2.legend(loc='best')
ax2.grid(True, alpha=0.3)

# --- Statistics Summary ---
if not sim3_data.empty:
    sim3_stats = {
        'mean': sim3_data['SINR_dB'].mean(),
        'std': sim3_data['SINR_dB'].std(),
        'min': sim3_data['SINR_dB'].min(),
        'max': sim3_data['SINR_dB'].max(),
        'count': len(sim3_data)
    }
    print(f"\nSimulation 3 SINR Statistics:")
    print(f"  Mean: {sim3_stats['mean']:.2f} dB")
    print(f"  Std:  {sim3_stats['std']:.2f} dB")
    print(f"  Min:  {sim3_stats['min']:.2f} dB")
    print(f"  Max:  {sim3_stats['max']:.2f} dB")
    print(f"  Count: {sim3_stats['count']} measurements")

if not sim4_data.empty:
    sim4_stats = {
        'mean': sim4_data['SINR_dB'].mean(),
        'std': sim4_data['SINR_dB'].std(),
        'min': sim4_data['SINR_dB'].min(),
        'max': sim4_data['SINR_dB'].max(),
        'count': len(sim4_data)
    }
    print(f"\nSimulation 4 SINR Statistics:")
    print(f"  Mean: {sim4_stats['mean']:.2f} dB")
    print(f"  Std:  {sim4_stats['std']:.2f} dB")
    print(f"  Min:  {sim4_stats['min']:.2f} dB")
    print(f"  Max:  {sim4_stats['max']:.2f} dB")
    print(f"  Count: {sim4_stats['count']} measurements")

plt.tight_layout()

# Ensure the output directory exists
output_dir = os.path.dirname(output_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"\nSINR comparison plot saved to {output_path}")
plt.show()
