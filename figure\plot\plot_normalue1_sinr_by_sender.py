import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import math

# --- Configuration ---
FIGURE_BASE_FILENAME = "normalue1_sinr_by_sender_sim3"
UES_PER_CHART = 4  # Maximum number of UEs per chart

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, "..", "..", "logs", "ue_logs", "simulation_3", "NormalUE-1.csv")
output_dir = script_dir

def load_normalue1_sinr_data():
    """
    Load SINR data for NormalUE-1, grouped by sender UE
    """
    try:
        # Try different encodings
        df = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                df = pd.read_csv(data_path, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print(f"Could not read {data_path} with any encoding")
            return {}
        
        # Filter rows that have SINR data and Related_UE_ID (received messages)
        sinr_data = df[
            (df['SINR_dB'] != 'N/A') & 
            (df['Related_UE_ID'] != 'N/A') & 
            (df['Related_UE_ID'].notna())
        ].copy()
        
        if sinr_data.empty:
            print("No SINR data found with Related_UE_ID")
            return {}
        
        # Convert SINR_dB to numeric
        sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
        sinr_data = sinr_data.dropna(subset=['SINR_dB'])
        
        # Group by sender UE
        grouped_data = {}
        for sender_ue in sinr_data['Related_UE_ID'].unique():
            sender_data = sinr_data[sinr_data['Related_UE_ID'] == sender_ue]
            if not sender_data.empty:
                grouped_data[sender_ue] = sender_data[['Time_ms', 'SINR_dB']].copy()
        
        return grouped_data
        
    except Exception as e:
        print(f"Error processing {data_path}: {e}")
        return {}

# --- Load Data ---
print("Loading NormalUE-1 SINR data grouped by sender...")
sender_data = load_normalue1_sinr_data()

if not sender_data:
    print("No SINR data found. Please check the data file.")
    exit()

print(f"Found SINR data from {len(sender_data)} different senders:")
for sender in sender_data.keys():
    print(f"  - {sender}: {len(sender_data[sender])} measurements")

# --- Create Multiple Plots with Subplots ---
plt.style.use('seaborn-v0_8-whitegrid')

# Convert sender_data to list for easier grouping
sender_list = list(sender_data.items())
total_senders = len(sender_list)
num_images = math.ceil(total_senders / UES_PER_CHART)

print(f"Creating {num_images} images for {total_senders} senders ({UES_PER_CHART} subplots per image)")

# Define colors for each UE
colors = plt.cm.tab10(np.linspace(0, 1, 10))

# Create images with subplots
for img_idx in range(num_images):
    start_idx = img_idx * UES_PER_CHART
    end_idx = min(start_idx + UES_PER_CHART, total_senders)
    image_senders = sender_list[start_idx:end_idx]
    num_subplots = len(image_senders)

    # Create subplot layout (2x2 for up to 4 UEs)
    if num_subplots == 1:
        fig, axes = plt.subplots(1, 1, figsize=(16, 8))
        axes = [axes]
    elif num_subplots == 2:
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    elif num_subplots == 3:
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        axes[3].set_visible(False)  # Hide the 4th subplot
    else:  # num_subplots == 4
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()

    # Plot each sender in its own subplot
    for i, (sender_ue, data) in enumerate(image_senders):
        ax = axes[i]

        # Sort by time for proper line plotting
        data_sorted = data.sort_values('Time_ms')

        # Plot SINR line
        ax.plot(data_sorted['Time_ms'], data_sorted['SINR_dB'],
               color=colors[i % len(colors)], linewidth=1.5, alpha=0.8,
               marker='o', markersize=1.5)

        # Subplot formatting
        ax.set_title(f'{sender_ue} SINR', fontsize=12, fontweight='bold')
        ax.set_xlabel('Time (ms)', fontsize=10)
        ax.set_ylabel('SINR (dB)', fontsize=10)
        ax.grid(True, alpha=0.3)

    # Overall title for the image
    fig.suptitle(f'NormalUE-1 Received SINR by Sender Vehicle (Simulation 3) - Image {img_idx + 1}/{num_images}',
                 fontsize=16, fontweight='bold')

    plt.tight_layout()

    # Save each image
    output_filename = f"{FIGURE_BASE_FILENAME}_part{img_idx + 1}.png"
    output_path = os.path.join(output_dir, output_filename)

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Image {img_idx + 1} with {num_subplots} subplots saved to {output_path}")

    plt.show()

# --- Print Detailed Statistics ---
print(f"\n=== DETAILED STATISTICS FOR NormalUE-1 ===")
for sender_ue, data in sender_data.items():
    print(f"\n{sender_ue}:")
    print(f"  Measurements: {len(data)}")
    print(f"  Mean SINR: {data['SINR_dB'].mean():.2f} dB")
    print(f"  Median SINR: {data['SINR_dB'].median():.2f} dB")
    print(f"  Std SINR: {data['SINR_dB'].std():.2f} dB")
    print(f"  Min SINR: {data['SINR_dB'].min():.2f} dB")
    print(f"  Max SINR: {data['SINR_dB'].max():.2f} dB")
    print(f"  Time range: {data['Time_ms'].min():.0f} - {data['Time_ms'].max():.0f} ms")

# All charts have been saved and displayed above
