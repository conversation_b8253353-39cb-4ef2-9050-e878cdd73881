import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import math

# --- Configuration ---
FIGURE_BASE_FILENAME = "normalue1_sinr_by_sender_sim3"
UES_PER_CHART = 4  # Maximum number of UEs per chart

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, "..", "..", "logs", "ue_logs", "simulation_3", "NormalUE-1.csv")
output_dir = script_dir

def load_normalue1_sinr_data():
    """
    Load SINR data for NormalUE-1, grouped by sender UE
    """
    try:
        # Try different encodings
        df = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                df = pd.read_csv(data_path, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print(f"Could not read {data_path} with any encoding")
            return {}
        
        # Filter rows that have SINR data and Related_UE_ID (received messages)
        sinr_data = df[
            (df['SINR_dB'] != 'N/A') & 
            (df['Related_UE_ID'] != 'N/A') & 
            (df['Related_UE_ID'].notna())
        ].copy()
        
        if sinr_data.empty:
            print("No SINR data found with Related_UE_ID")
            return {}
        
        # Convert SINR_dB to numeric
        sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
        sinr_data = sinr_data.dropna(subset=['SINR_dB'])
        
        # Group by sender UE
        grouped_data = {}
        for sender_ue in sinr_data['Related_UE_ID'].unique():
            sender_data = sinr_data[sinr_data['Related_UE_ID'] == sender_ue]
            if not sender_data.empty:
                grouped_data[sender_ue] = sender_data[['Time_ms', 'SINR_dB']].copy()
        
        return grouped_data
        
    except Exception as e:
        print(f"Error processing {data_path}: {e}")
        return {}

# --- Load Data ---
print("Loading NormalUE-1 SINR data grouped by sender...")
sender_data = load_normalue1_sinr_data()

if not sender_data:
    print("No SINR data found. Please check the data file.")
    exit()

print(f"Found SINR data from {len(sender_data)} different senders:")
for sender in sender_data.keys():
    print(f"  - {sender}: {len(sender_data[sender])} measurements")

# --- Create Multiple Plots ---
plt.style.use('seaborn-v0_8-whitegrid')

# Convert sender_data to list for easier grouping
sender_list = list(sender_data.items())
total_senders = len(sender_list)
num_charts = math.ceil(total_senders / UES_PER_CHART)

print(f"Creating {num_charts} charts for {total_senders} senders ({UES_PER_CHART} UEs per chart)")

# Define colors and line styles
colors = plt.cm.tab10(np.linspace(0, 1, 10))  # Use 10 distinct colors
line_styles = ['-', '--', '-.', ':', '-', '--', '-.', ':', '-', '--']

# Create charts
for chart_idx in range(num_charts):
    start_idx = chart_idx * UES_PER_CHART
    end_idx = min(start_idx + UES_PER_CHART, total_senders)
    chart_senders = sender_list[start_idx:end_idx]

    # Create figure
    fig, ax = plt.subplots(figsize=(16, 10))

    # Plot SINR for each sender in this chart
    for i, (sender_ue, data) in enumerate(chart_senders):
        color = colors[i % len(colors)]
        line_style = line_styles[i % len(line_styles)]

        # Sort by time for proper line plotting
        data_sorted = data.sort_values('Time_ms')

        ax.plot(data_sorted['Time_ms'], data_sorted['SINR_dB'],
               color=color, linestyle=line_style, linewidth=1.5, alpha=0.8,
               label=f'{sender_ue} ({len(data)} pts)', marker='o', markersize=2)

    # --- Plot Formatting ---
    chart_title = f'NormalUE-1 Received SINR by Sender Vehicle (Simulation 3) - Chart {chart_idx + 1}/{num_charts}'
    ax.set_title(chart_title, fontsize=16, fontweight='bold')
    ax.set_xlabel('Time (ms)', fontsize=12)
    ax.set_ylabel('SINR (dB)', fontsize=12)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save each chart
    output_filename = f"{FIGURE_BASE_FILENAME}_part{chart_idx + 1}.png"
    output_path = os.path.join(output_dir, output_filename)

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Chart {chart_idx + 1} saved to {output_path}")

    plt.show()

# --- Print Detailed Statistics ---
print(f"\n=== DETAILED STATISTICS FOR NormalUE-1 ===")
for sender_ue, data in sender_data.items():
    print(f"\n{sender_ue}:")
    print(f"  Measurements: {len(data)}")
    print(f"  Mean SINR: {data['SINR_dB'].mean():.2f} dB")
    print(f"  Median SINR: {data['SINR_dB'].median():.2f} dB")
    print(f"  Std SINR: {data['SINR_dB'].std():.2f} dB")
    print(f"  Min SINR: {data['SINR_dB'].min():.2f} dB")
    print(f"  Max SINR: {data['SINR_dB'].max():.2f} dB")
    print(f"  Time range: {data['Time_ms'].min():.0f} - {data['Time_ms'].max():.0f} ms")

# All charts have been saved and displayed above
