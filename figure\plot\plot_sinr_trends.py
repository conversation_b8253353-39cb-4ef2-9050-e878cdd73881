import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from glob import glob

# --- Configuration ---
FIGURE_FILENAME = "sinr_trends_sim3_sim4.png"
TIME_WINDOW = 50  # ms - smaller window for more detailed trends

# --- File Paths ---
script_dir = os.path.dirname(os.path.abspath(__file__))
sim3_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_3")
sim4_path = os.path.join(script_dir, "..", "..", "output", "nr_v2x_simulator_simulation_results", "simulation_4")
output_path = os.path.join(script_dir, FIGURE_FILENAME)

def load_all_sinr_data(simulation_path, simulation_name):
    """
    Load all SINR data from a simulation directory
    """
    csv_files = glob(os.path.join(simulation_path, "*.csv"))
    all_data = []
    
    for csv_file in csv_files:
        # Skip attacker files for cleaner visualization
        if 'Attacker' in os.path.basename(csv_file):
            continue
            
        try:
            # Try different encodings
            df = None
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                continue
            
            # Filter SINR data
            sinr_data = df[df['SINR_dB'] != 'N/A'].copy()
            if not sinr_data.empty:
                sinr_data['SINR_dB'] = pd.to_numeric(sinr_data['SINR_dB'], errors='coerce')
                sinr_data = sinr_data.dropna(subset=['SINR_dB'])
                
                if not sinr_data.empty:
                    ue_name = os.path.basename(csv_file).replace('.csv', '')
                    sinr_data['UE_ID'] = ue_name
                    sinr_data['Simulation'] = simulation_name
                    all_data.append(sinr_data[['Time_ms', 'SINR_dB', 'UE_ID', 'Simulation']])
        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue
    
    if all_data:
        return pd.concat(all_data, ignore_index=True)
    else:
        return pd.DataFrame()

def calculate_sinr_trends(df, time_window):
    """
    Calculate SINR statistics over time windows
    """
    if df.empty:
        return pd.DataFrame()
    
    max_time = df['Time_ms'].max()
    time_bins = np.arange(0, max_time + time_window, time_window)
    
    trend_data = []
    
    for i in range(len(time_bins) - 1):
        start_time, end_time = time_bins[i], time_bins[i+1]
        
        window_data = df[
            (df['Time_ms'] >= start_time) & (df['Time_ms'] < end_time)
        ]
        
        if not window_data.empty:
            trend_data.append({
                'Time': (start_time + end_time) / 2,
                'Mean_SINR': window_data['SINR_dB'].mean(),
                'Median_SINR': window_data['SINR_dB'].median(),
                'Std_SINR': window_data['SINR_dB'].std(),
                'Min_SINR': window_data['SINR_dB'].min(),
                'Max_SINR': window_data['SINR_dB'].max(),
                'Count': len(window_data)
            })
    
    return pd.DataFrame(trend_data)

# --- Load Data ---
print("Loading and processing simulation data...")
sim3_data = load_all_sinr_data(sim3_path, "Simulation 3")
sim4_data = load_all_sinr_data(sim4_path, "Simulation 4")

if sim3_data.empty and sim4_data.empty:
    print("No SINR data found. Please check the data files.")
    exit()

# Calculate trends
sim3_trends = calculate_sinr_trends(sim3_data, TIME_WINDOW)
sim4_trends = calculate_sinr_trends(sim4_data, TIME_WINDOW)

# --- Create Plot ---
plt.style.use('seaborn-v0_8-whitegrid')
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

# Plot 1: Mean SINR trends
if not sim3_trends.empty:
    ax1.plot(sim3_trends['Time'], sim3_trends['Mean_SINR'], 
            color='blue', linewidth=2, marker='o', markersize=3, 
            label='Simulation 3', alpha=0.8)
    ax1.fill_between(sim3_trends['Time'], 
                    sim3_trends['Mean_SINR'] - sim3_trends['Std_SINR'], 
                    sim3_trends['Mean_SINR'] + sim3_trends['Std_SINR'], 
                    alpha=0.2, color='blue')

if not sim4_trends.empty:
    ax1.plot(sim4_trends['Time'], sim4_trends['Mean_SINR'], 
            color='red', linewidth=2, marker='s', markersize=3, 
            label='Simulation 4', alpha=0.8)
    ax1.fill_between(sim4_trends['Time'], 
                    sim4_trends['Mean_SINR'] - sim4_trends['Std_SINR'], 
                    sim4_trends['Mean_SINR'] + sim4_trends['Std_SINR'], 
                    alpha=0.2, color='red')

ax1.set_title('Mean SINR Trends (with Standard Deviation)', fontsize=12)
ax1.set_xlabel('Time (ms)')
ax1.set_ylabel('SINR (dB)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Median SINR trends
if not sim3_trends.empty:
    ax2.plot(sim3_trends['Time'], sim3_trends['Median_SINR'], 
            color='blue', linewidth=2, marker='o', markersize=3, 
            label='Simulation 3', alpha=0.8)

if not sim4_trends.empty:
    ax2.plot(sim4_trends['Time'], sim4_trends['Median_SINR'], 
            color='red', linewidth=2, marker='s', markersize=3, 
            label='Simulation 4', alpha=0.8)

ax2.set_title('Median SINR Trends', fontsize=12)
ax2.set_xlabel('Time (ms)')
ax2.set_ylabel('SINR (dB)')
ax2.legend()
ax2.grid(True, alpha=0.3)

# Plot 3: Min/Max SINR ranges
if not sim3_trends.empty:
    ax3.fill_between(sim3_trends['Time'], sim3_trends['Min_SINR'], sim3_trends['Max_SINR'], 
                    alpha=0.3, color='blue', label='Simulation 3 Range')
    ax3.plot(sim3_trends['Time'], sim3_trends['Mean_SINR'], 
            color='blue', linewidth=2, label='Simulation 3 Mean')

if not sim4_trends.empty:
    ax3.fill_between(sim4_trends['Time'], sim4_trends['Min_SINR'], sim4_trends['Max_SINR'], 
                    alpha=0.3, color='red', label='Simulation 4 Range')
    ax3.plot(sim4_trends['Time'], sim4_trends['Mean_SINR'], 
            color='red', linewidth=2, label='Simulation 4 Mean')

ax3.set_title('SINR Range (Min-Max) with Mean', fontsize=12)
ax3.set_xlabel('Time (ms)')
ax3.set_ylabel('SINR (dB)')
ax3.legend()
ax3.grid(True, alpha=0.3)

# Plot 4: Data point counts
if not sim3_trends.empty:
    ax4.plot(sim3_trends['Time'], sim3_trends['Count'], 
            color='blue', linewidth=2, marker='o', markersize=3, 
            label='Simulation 3', alpha=0.8)

if not sim4_trends.empty:
    ax4.plot(sim4_trends['Time'], sim4_trends['Count'], 
            color='red', linewidth=2, marker='s', markersize=3, 
            label='Simulation 4', alpha=0.8)

ax4.set_title('Number of SINR Measurements per Time Window', fontsize=12)
ax4.set_xlabel('Time (ms)')
ax4.set_ylabel('Count')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.suptitle(f'SINR Trends Analysis (Time Window: {TIME_WINDOW}ms)', fontsize=16)
plt.tight_layout()

# Print summary statistics
print(f"\n=== SUMMARY STATISTICS ===")
if not sim3_data.empty:
    print(f"\nSimulation 3 Overall:")
    print(f"  Total measurements: {len(sim3_data)}")
    print(f"  Mean SINR: {sim3_data['SINR_dB'].mean():.2f} dB")
    print(f"  Median SINR: {sim3_data['SINR_dB'].median():.2f} dB")
    print(f"  Std SINR: {sim3_data['SINR_dB'].std():.2f} dB")

if not sim4_data.empty:
    print(f"\nSimulation 4 Overall:")
    print(f"  Total measurements: {len(sim4_data)}")
    print(f"  Mean SINR: {sim4_data['SINR_dB'].mean():.2f} dB")
    print(f"  Median SINR: {sim4_data['SINR_dB'].median():.2f} dB")
    print(f"  Std SINR: {sim4_data['SINR_dB'].std():.2f} dB")

# Save plot
output_dir = os.path.dirname(output_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"\nSINR trends plot saved to {output_path}")
plt.show()
